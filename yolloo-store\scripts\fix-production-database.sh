#!/bin/bash

# 修复生产环境数据库Schema问题
# 此脚本将统一mobile-api和主应用的数据库schema

set -e

echo "=== 修复生产环境数据库Schema问题 ==="
echo ""

# 检查当前目录
if [ ! -f "prisma/schema.prisma" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "错误: DATABASE_URL 环境变量未设置"
    echo "请确保在 .env.production 文件中设置了正确的 DATABASE_URL"
    exit 1
fi

echo "当前 DATABASE_URL: $DATABASE_URL"
echo ""

# 询问用户是否继续
echo "此脚本将执行以下操作:"
echo "1. 备份当前数据库schema"
echo "2. 执行统一schema迁移"
echo "3. 添加mobile-api需要的表和字段"
echo "4. 更新两个服务的Prisma客户端"
echo ""
read -p "是否继续? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "=== 步骤 1: 检查当前数据库状态 ==="
echo "检查迁移状态..."
npx prisma migrate status || echo "迁移状态检查完成"

echo ""
echo "=== 步骤 2: 生成Prisma客户端 ==="
npx prisma generate
if [ $? -ne 0 ]; then
    echo "错误: 生成Prisma客户端失败"
    exit 1
fi

echo ""
echo "=== 步骤 3: 执行数据库迁移 ==="
echo "部署新的迁移..."
npx prisma migrate deploy
if [ $? -ne 0 ]; then
    echo "错误: 数据库迁移失败"
    echo "请检查数据库连接和迁移文件"
    exit 1
fi

echo ""
echo "=== 步骤 4: 验证数据库schema ==="
echo "检查表是否存在..."

# 使用psql检查关键表是否存在
if command -v psql &> /dev/null; then
    echo "检查关键表..."
    psql "$DATABASE_URL" -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('Notification', 'Wallet', 'UserCoupon', 'Continent', 'Product');" || echo "无法连接数据库进行验证"
else
    echo "psql未安装，跳过数据库验证"
fi

echo ""
echo "=== 步骤 5: 更新mobile-api配置 ==="

# 确保mobile-api使用相同的schema
if [ -f "mobile-api/prisma/schema.prisma" ]; then
    echo "备份mobile-api原始schema..."
    cp mobile-api/prisma/schema.prisma mobile-api/prisma/schema.prisma.backup.$(date +%Y%m%d_%H%M%S)
    
    echo "复制统一schema到mobile-api..."
    cp prisma/schema.prisma mobile-api/prisma/schema.prisma
    
    echo "生成mobile-api Prisma客户端..."
    cd mobile-api
    npx prisma generate
    if [ $? -ne 0 ]; then
        echo "错误: 生成mobile-api Prisma客户端失败"
        exit 1
    fi
    cd ..
fi

echo ""
echo "=== 步骤 6: 重建Docker镜像 ==="
echo "重建应用镜像以包含新的schema..."

# 停止现有容器
echo "停止现有容器..."
docker-compose -f docker-compose.production.yml down || echo "容器已停止"

# 重建镜像
echo "重建Docker镜像..."
docker-compose -f docker-compose.production.yml build --no-cache

# 启动服务
echo "启动服务..."
docker-compose -f docker-compose.production.yml up -d

echo ""
echo "=== 修复完成 ==="
echo "✓ 数据库schema已统一"
echo "✓ 迁移已执行"
echo "✓ mobile-api schema已同步"
echo "✓ Docker镜像已重建"
echo "✓ 服务已重启"
echo ""
echo "请检查应用日志确保没有错误:"
echo "  docker-compose -f docker-compose.production.yml logs -f"
echo ""
echo "如果仍有问题，请检查:"
echo "1. 两个服务是否使用相同的DATABASE_URL"
echo "2. 数据库连接是否正常"
echo "3. 迁移是否完全执行"
