{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/esims/page": "app/esims/page.js", "/api/admin/products/route": "app/api/admin/products/route.js", "/api/admin/products/countries/route": "app/api/admin/products/countries/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/admin/page": "app/admin/page.js", "/account/page": "app/account/page.js", "/admin/products/page": "app/admin/products/page.js", "/admin/products/sync/page": "app/admin/products/sync/page.js"}