#!/bin/bash

# 统一数据库Schema脚本
# 此脚本将mobile-api和主应用的数据库schema统一

set -e

echo "=== 统一数据库Schema脚本 ==="
echo ""

# 检查当前目录
if [ ! -f "prisma/schema.prisma" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 显示当前数据库连接信息
echo "检查数据库连接..."
if [ -z "$DATABASE_URL" ]; then
    echo "警告: DATABASE_URL 环境变量未设置"
    echo "请确保在 .env 文件中设置了正确的 DATABASE_URL"
fi

echo "当前 DATABASE_URL: ${DATABASE_URL:-未设置}"
echo ""

# 检查主应用的迁移状态
echo "检查主应用迁移状态..."
npx prisma migrate status
echo ""

# 询问用户是否继续
read -p "是否继续执行统一schema迁移? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "=== 步骤 1: 生成Prisma客户端 ==="
npx prisma generate
if [ $? -ne 0 ]; then
    echo "错误: 生成Prisma客户端失败"
    exit 1
fi

echo ""
echo "=== 步骤 2: 执行数据库迁移 ==="
npx prisma migrate deploy
if [ $? -ne 0 ]; then
    echo "错误: 数据库迁移失败"
    echo "请检查数据库连接和迁移文件"
    exit 1
fi

echo ""
echo "=== 步骤 3: 验证数据库schema ==="
npx prisma db pull --print
if [ $? -ne 0 ]; then
    echo "警告: 无法验证数据库schema"
fi

echo ""
echo "=== 步骤 4: 更新mobile-api配置 ==="

# 确保mobile-api使用相同的schema
if [ -f "mobile-api/prisma/schema.prisma" ]; then
    echo "备份mobile-api原始schema..."
    cp mobile-api/prisma/schema.prisma mobile-api/prisma/schema.prisma.backup.$(date +%Y%m%d_%H%M%S)
    
    echo "复制统一schema到mobile-api..."
    cp prisma/schema.prisma mobile-api/prisma/schema.prisma
    
    echo "生成mobile-api Prisma客户端..."
    cd mobile-api
    npx prisma generate
    if [ $? -ne 0 ]; then
        echo "错误: 生成mobile-api Prisma客户端失败"
        exit 1
    fi
    cd ..
fi

echo ""
echo "=== 统一完成 ==="
echo "✓ 主应用schema已更新"
echo "✓ 数据库迁移已执行"
echo "✓ mobile-api schema已同步"
echo "✓ Prisma客户端已生成"
echo ""
echo "注意事项:"
echo "1. 确保两个服务都使用相同的 DATABASE_URL"
echo "2. 重启所有服务以使用新的schema"
echo "3. 检查应用日志确保没有数据库错误"
echo ""
echo "如果遇到问题，可以使用以下命令回滚:"
echo "  npx prisma migrate reset"
echo "  git checkout -- prisma/schema.prisma mobile-api/prisma/schema.prisma"
