# 数据库Schema统一解决方案

## 问题描述

生产环境中mobile-api报错500，错误信息显示多个表不存在：
- `public.Notification` 表不存在
- `public.Wallet` 表不存在  
- `public.UserCoupon` 表不存在
- `public.Continent` 表不存在
- `Product.popularityScore` 列不存在

## 根本原因

1. **Schema不一致**: mobile-api和主应用使用不同的schema.prisma文件
2. **数据库架构不同步**: mobile-api的schema包含额外的模型，但这些模型没有在生产数据库中创建
3. **迁移未执行**: mobile-api特有的迁移没有在生产环境执行

## 解决方案

### 方案概述
统一两个服务的数据库schema，让它们使用相同的数据库架构。

### 已完成的修改

1. **统一Schema文件**
   - 将mobile-api需要的所有模型添加到主应用的`prisma/schema.prisma`
   - 更新mobile-api使用主应用的schema
   - 添加了以下模型：
     - Notification (通知系统)
     - Wallet, Transaction, PaymentCard (钱包系统)
     - Coupon, UserCoupon (优惠券系统)
     - Continent, Country, MobileOperator (地理信息)
     - PackageUsage, RechargeHistory (使用记录)
     - SocialAccount, ProductView, Upload (其他功能)
     - Page, Article, Banner, HomeFeature, TravelTip (内容管理)

2. **添加缺失字段**
   - Product模型添加`popularityScore`和`isPopular`字段
   - User模型添加所有mobile-api需要的关联

3. **创建迁移文件**
   - `prisma/migrations/20250625000000_add_mobile_api_models/migration.sql`
   - 包含所有新表、字段、索引和外键约束

### 执行步骤

#### 方法1: 使用自动化脚本（推荐）

```bash
# 在生产环境执行
cd yolloo-store
./scripts/fix-production-database.sh
```

#### 方法2: 手动执行

```bash
# 1. 生成Prisma客户端
npx prisma generate

# 2. 执行数据库迁移
npx prisma migrate deploy

# 3. 更新mobile-api schema
cp prisma/schema.prisma mobile-api/prisma/schema.prisma

# 4. 生成mobile-api Prisma客户端
cd mobile-api
npx prisma generate
cd ..

# 5. 重建并重启Docker容器
docker-compose -f docker-compose.production.yml down
docker-compose -f docker-compose.production.yml build --no-cache
docker-compose -f docker-compose.production.yml up -d
```

### 验证步骤

1. **检查迁移状态**
   ```bash
   npx prisma migrate status
   ```

2. **验证表存在**
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name IN ('Notification', 'Wallet', 'UserCoupon', 'Continent');
   ```

3. **检查应用日志**
   ```bash
   docker-compose -f docker-compose.production.yml logs -f mobile-api
   ```

### 重要注意事项

1. **数据库URL统一**: 确保两个服务使用相同的`DATABASE_URL`
2. **备份数据**: 执行前建议备份生产数据库
3. **服务重启**: 迁移后需要重启所有服务
4. **监控日志**: 密切关注应用启动日志

### 环境变量检查

确保`.env.production`文件中：
```env
DATABASE_URL="postgresql://user:password@host:port/database"
# 不要使用 MOBILE_API_DATABASE_URL，两个服务应该使用相同的数据库
```

### 故障排除

如果仍有问题：

1. **检查数据库连接**
   ```bash
   npx prisma db pull
   ```

2. **重置迁移（谨慎使用）**
   ```bash
   npx prisma migrate reset
   ```

3. **手动执行迁移SQL**
   ```bash
   psql $DATABASE_URL -f prisma/migrations/20250625000000_add_mobile_api_models/migration.sql
   ```

### 长期维护

1. **保持Schema同步**: 今后所有数据库变更都应该在主应用的schema中进行
2. **统一迁移流程**: 使用主应用的迁移系统管理所有数据库变更
3. **定期检查**: 定期验证两个服务的schema一致性

## 联系支持

如果遇到问题，请提供：
- 错误日志
- 迁移状态输出
- 数据库连接信息（隐藏敏感信息）
