generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String                @id @default(cuid())
  name                 String?
  email                String?               @unique
  emailVerified        DateTime?
  image                String?
  role                 UserRole              @default(CUSTOMER)
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  hashedPassword       String?
  accounts             Account[]
  addresses            Address[]
  affiliate            AffiliateProfile?
  cart                 CartItem[]
  marketingCommissions MarketingCommission[]
  orders               Order[]
  preSaleSubscriptions PreSaleSubscription[]
  reviews              Review[]
  sessions             Session[]
  wishlist             WishlistItem[]
  yollooCards          YollooCard[]
  passwordResetToken   PasswordResetToken?
  loginHistory         UserLoginHistory[]
  notifications        Notification[]
  wallet               Wallet?
  userCoupons          UserCoupon[]
  packageUsages        PackageUsage[]
  rechargeHistories    RechargeHistory[]
  socialAccounts       SocialAccount[]
  productViews         ProductView[]
  uploads              Upload[]
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Product {
  id                 String             @id @default(cuid())
  name               String
  description        String
  websiteDescription String             @default("")
  price              Float
  images             String[]
  categoryId         String
  stock              Int
  specifications     Json               @default("{}")
  status             ProductStatus      @default(ACTIVE)
  sku                String             @unique
  requiredUID        Boolean            @default(false)
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  mcc                String?
  off_shelve         Boolean            @default(false)
  dataSize           Float? // Data size in MB
  planType           String? // "Daily" or "Total"
  country            String? // Country name, may be comma or semicolon separated
  countryCode        String? // Country code, may be comma or semicolon separated
  odooLastSyncAt     DateTime? // When the product was last synced with Odoo
  popularityScore    Float?             @default(0) // Popularity score for ranking
  isPopular          Boolean            @default(false) // Whether product is marked as popular
  cartItems          CartItem[]
  esims              Esim[]
  category           Category           @relation(fields: [categoryId], references: [id])
  parameters         ProductParameter[]
  variants           ProductVariant[]
  reviews            Review[]
  wishlistItems      WishlistItem[]
  productViews       ProductView[]
  orderItems         OrderItem[]

  // 添加性能优化索引
  @@index([status, off_shelve, createdAt])
  @@index([categoryId, status, off_shelve])
  @@index([price])
  @@index([name])
  @@index([country])
}

model ProductVariant {
  id           String      @id @default(cuid())
  price        Decimal     @db.Decimal(10, 2)
  currency     String      @default("USD")
  productId    String
  variantCode  String?     @unique // Matches variant_code from Odoo
  duration     Int? // Billing period duration
  durationType String? // "day" or "month"
  attributes   Json
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  cartItems    CartItem[]
  product      Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model ProductParameter {
  id        String   @id @default(cuid())
  code      String
  name      String
  value     String
  productId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model Category {
  id          String     @id @default(cuid())
  name        String
  description String?
  image       String?
  parentId    String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  parent      Category?  @relation("CategoryToCategory", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryToCategory")
  products    Product[]
}

model CartItem {
  id        String          @id @default(cuid())
  userId    String
  productId String
  variantId String?
  quantity  Int
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  product   Product         @relation(fields: [productId], references: [id])
  user      User            @relation(fields: [userId], references: [id])
  variant   ProductVariant? @relation(fields: [variantId], references: [id])

  @@unique([userId, productId, variantId])
}

model WishlistItem {
  id        String   @id @default(cuid())
  userId    String
  productId String
  createdAt DateTime @default(now())
  product   Product  @relation(fields: [productId], references: [id])
  user      User     @relation(fields: [userId], references: [id])
}

model Order {
  id                      String             @id @default(cuid())
  userId                  String
  total                   Float
  status                  OrderStatus        @default(PENDING)
  addressId               String?
  shippingAddressSnapshot Json?              @db.JsonB
  paymentId               String?
  createdAt               DateTime           @default(now())
  updatedAt               DateTime           @updatedAt
  referralCode            String?
  affiliateReferral       AffiliateReferral?
  shippingAddress         Address?           @relation(fields: [addressId], references: [id])
  payment                 Payment?           @relation(fields: [paymentId], references: [id])
  user                    User               @relation(fields: [userId], references: [id])
  items                   OrderItem[]
  odooStatuses            OdooOrderStatus[]
}

model OdooOrderStatus {
  id             String   @id @default(cuid())
  orderId        String
  order          Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  variantCode    String   @default("default")
  odooOrderRef   String?
  status         String
  description    String?
  productName    String?
  isDigital      Boolean  @default(false)
  deliveredQty   Int      @default(0)
  trackingNumber String?
  planState      String?
  uid            String?
  lastCheckedAt  DateTime @default(now())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([orderId, variantCode, uid])
}

model Payment {
  id            String        @id @default(cuid())
  amount        Float
  currency      String        @default("USD")
  status        PaymentStatus @default(PENDING)
  provider      String
  paymentMethod String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  orders        Order[]
}

model Address {
  id         String      @id @default(cuid())
  userId     String?
  type       AddressType @default(SHIPPING)
  name       String
  phone      String
  address1   String
  address2   String?
  city       String
  state      String
  postalCode String
  country    String
  isDefault  Boolean     @default(false)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  user       User?       @relation(fields: [userId], references: [id])
  orders     Order[]
}

model OrderItem {
  id                 String             @id @default(cuid())
  orderId            String
  productCode        String?
  variantCode        String?
  variantText        String?
  quantity           Int
  price              Float
  uid                String?
  lpaString          String?
  order              Order              @relation(fields: [orderId], references: [id])
}

model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int
  comment   String
  createdAt DateTime @default(now())
  product   Product  @relation(fields: [productId], references: [id])
  user      User     @relation(fields: [userId], references: [id])
}

model Settings {
  id        String   @id @default("1")
  siteName  String   @default("Yolloo Store")
  language  String   @default("en")
  theme     String   @default("light")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model YollooCard {
  id             String    @id @default(cuid())
  number         String    @unique
  status         String    @default("Inactive")
  type           String
  customName     String?
  activationDate DateTime?
  expiryDate     DateTime?
  userId         String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  esims          Esim[]
  user           User?     @relation(fields: [userId], references: [id])

  @@index([userId])
}

model Esim {
  id             String       @id @default(cuid())
  iccid          String       @unique
  status         String       @default("Inactive")
  activationDate DateTime?
  expiryDate     DateTime?
  yollooCardId   String
  productId      String
  profileId      String?
  planId         String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  plan           EsimPlan?    @relation(fields: [planId], references: [id])
  product        Product      @relation(fields: [productId], references: [id])
  profile        EsimProfile? @relation(fields: [profileId], references: [id])
  yollooCard     YollooCard   @relation(fields: [yollooCardId], references: [id], onDelete: Cascade)

  @@index([yollooCardId])
  @@index([productId])
  @@index([profileId])
  @@index([planId])
}

model EsimProfile {
  id             String    @id @default(cuid())
  imsi           String    @unique
  carrierName    String
  networkType    String
  serviceArea    String
  roamingEnabled Boolean   @default(false)
  status         String    @default("Inactive")
  activationDate DateTime?
  expiryDate     DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  esims          Esim[]
}

model EsimPlan {
  id             String   @id @default(cuid())
  name           String
  description    String
  dataLimit      Float
  duration       Int
  price          Float
  currency       String   @default("USD")
  features       Json
  roamingRegions String[]
  status         String   @default("ACTIVE")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  esims          Esim[]
}

model AffiliateProfile {
  id             String                 @id @default(cuid())
  userId         String                 @unique
  code           String                 @unique
  commissionRate Float                  @default(0.1)
  discountRate   Float                  @default(0.05)
  totalEarnings  Float                  @default(0)
  status         AffiliateStatus        @default(ACTIVE)
  createdAt      DateTime               @default(now())
  updatedAt      DateTime               @updatedAt
  organizationId String?
  isAdmin        Boolean                @default(false)
  user           User                   @relation(fields: [userId], references: [id])
  organization   AffiliateOrganization? @relation(fields: [organizationId], references: [id])
  referrals      AffiliateReferral[]
  visits         AffiliateVisit[]
  withdrawals    AffiliateWithdrawal[]
  invites        OrganizationInvite[]
}

model AffiliateReferral {
  id                       String                  @id @default(cuid())
  affiliateId              String
  orderId                  String                  @unique
  commissionAmount         Float
  status                   ReferralStatus          @default(PENDING)
  createdAt                DateTime                @default(now())
  updatedAt                DateTime                @updatedAt
  organizationCommissionId String?
  affiliate                AffiliateProfile        @relation(fields: [affiliateId], references: [id])
  order                    Order                   @relation(fields: [orderId], references: [id])
  organizationCommission   OrganizationCommission? @relation(fields: [organizationCommissionId], references: [id])
}

model AffiliateWithdrawal {
  id             String           @id @default(cuid())
  affiliateId    String
  amount         Float
  status         WithdrawalStatus @default(PENDING)
  paymentMethod  String
  paymentDetails Json?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  affiliate      AffiliateProfile @relation(fields: [affiliateId], references: [id])
}

model AffiliateVisit {
  id               String                 @id @default(cuid())
  affiliateId      String
  source           String
  path             String
  userAgent        String
  referrer         String
  createdAt        DateTime               @default(now())
  convertedToOrder Boolean                @default(false)
  orderId          String?
  organizationId   String?
  affiliate        AffiliateProfile       @relation(fields: [affiliateId], references: [id])
  organization     AffiliateOrganization? @relation(fields: [organizationId], references: [id])
}

model PreSaleSubscription {
  id              String                    @id @default(cuid())
  email           String                    @unique
  referralCode    String?
  status          PreSaleSubscriptionStatus @default(PENDING)
  discountCode    String?
  subscribedAt    DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt
  ipAddress       String?
  userAgent       String?
  convertedToUser Boolean                   @default(false)
  userId          String?
  user            User?                     @relation(fields: [userId], references: [id])
}

model AffiliateOrganization {
  id                 String                   @id @default(cuid())
  name               String
  description        String?
  code               String                   @unique
  logo               String?
  commissionRate     Float                    @default(0.12) // Higher than individual rate
  discountRate       Float                    @default(0.05)
  totalEarnings      Float                    @default(0)
  status             AffiliateStatus          @default(ACTIVE)
  createdAt          DateTime                 @default(now())
  updatedAt          DateTime                 @updatedAt
  members            AffiliateProfile[]
  withdrawals        OrganizationWithdrawal[]
  visits             AffiliateVisit[]
  commissions        OrganizationCommission[]
  OrganizationInvite OrganizationInvite[]
}

model OrganizationWithdrawal {
  id             String                @id @default(cuid())
  organizationId String
  amount         Float
  status         WithdrawalStatus      @default(PENDING)
  paymentMethod  String
  paymentDetails Json?
  createdAt      DateTime              @default(now())
  updatedAt      DateTime              @updatedAt
  organization   AffiliateOrganization @relation(fields: [organizationId], references: [id])
}

model OrganizationCommission {
  id               String                @id @default(cuid())
  organizationId   String
  commissionAmount Float
  status           ReferralStatus        @default(PENDING)
  createdAt        DateTime              @default(now())
  updatedAt        DateTime              @updatedAt
  organization     AffiliateOrganization @relation(fields: [organizationId], references: [id])
  referrals        AffiliateReferral[]
}

model OrganizationInvite {
  id             String                   @id @default(cuid())
  organizationId String
  affiliateId    String?
  email          String?
  status         OrganizationInviteStatus @default(PENDING)
  inviteCode     String                   @unique
  expiresAt      DateTime?
  createdAt      DateTime                 @default(now())
  updatedAt      DateTime                 @updatedAt
  organization   AffiliateOrganization    @relation(fields: [organizationId], references: [id])
  affiliate      AffiliateProfile?        @relation(fields: [affiliateId], references: [id])
  commissionRate Float?                   @default(0.5)
  isAdmin        Boolean?                 @default(false)
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String   @unique
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt DateTime
  createdAt DateTime @default(now())
}

model UserLoginHistory {
  id              String   @id @default(cuid())
  userId          String
  loginTimestamp  DateTime @default(now())
  ipAddress       String?
  userAgent       String?
  loginMethod     String?  // "password", "google", etc.
  isSuccessful    Boolean  @default(true)
  additionalInfo  Json?    @default("{}")
  createdAt       DateTime @default(now())

  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([loginTimestamp])
}

enum UserRole {
  ADMIN
  CUSTOMER
  STAFF
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
  DELETED
}

enum OrderStatus {
  PENDING
  PAID
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum AddressType {
  SHIPPING
  BILLING
}

enum AffiliateStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum ReferralStatus {
  PENDING
  APPROVED
  REJECTED
  PAID
}

enum WithdrawalStatus {
  PENDING
  COMPLETED
  REJECTED
}

enum PreSaleSubscriptionStatus {
  PENDING
  CONFIRMED
  UNSUBSCRIBED
}

enum MarketingCommissionStatus {
  PENDING
  APPROVED
  REJECTED
  PAID
}

enum OrganizationInviteStatus {
  PENDING
  ACCEPTED
  REJECTED
  EXPIRED
}

model MarketingCommission {
  id          String                    @id @default(cuid())
  userId      String
  amount      Float
  status      MarketingCommissionStatus @default(PENDING)
  description String?
  createdAt   DateTime                  @default(now())
  updatedAt   DateTime                  @updatedAt
  user        User                      @relation(fields: [userId], references: [id])
}

model SyncLog {
  id        String    @id @default(cuid())
  syncId    String    // 同步任务ID
  level     String    // 日志级别: INFO, WARNING, ERROR, DEBUG
  message   String    // 日志消息
  data      String?   // 额外数据，JSON格式
  timestamp DateTime  @default(now())

  @@index([syncId])
  @@index([level])
  @@index([timestamp])
}

model SyncTask {
  id          String    @id @default(cuid())
  syncId      String    @unique // 同步任务ID
  status      String    // 任务状态: PENDING, RUNNING, COMPLETED, FAILED
  options     Json      // 同步选项
  stats       Json?     // 同步统计信息
  startTime   DateTime  @default(now())
  endTime     DateTime?
  duration    Int?      // 持续时间（毫秒）
  createdBy   String?   // 创建者ID

  @@index([status])
  @@index([startTime])
}

// 通知系统模型
model Notification {
  id        String            @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  content   String
  data      Json?             @default("{}")
  isRead    Boolean           @default(false)
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, isRead])
  @@index([createdAt])
}

// 钱包系统模型
model Wallet {
  id           String        @id @default(cuid())
  userId       String        @unique
  balance      Float         @default(0)
  currency     String        @default("USD")
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[]
  paymentCards PaymentCard[]
}

model Transaction {
  id          String            @id @default(cuid())
  walletId    String
  type        TransactionType
  amount      Float
  currency    String            @default("USD")
  description String
  status      TransactionStatus @default(PENDING)
  reference   String?           // 外部参考号
  metadata    Json?             @default("{}")
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  wallet      Wallet            @relation(fields: [walletId], references: [id], onDelete: Cascade)

  @@index([walletId])
  @@index([type, status])
  @@index([createdAt])
}

model PaymentCard {
  id           String   @id @default(cuid())
  walletId     String
  cardNumber   String   // 加密存储
  cardHolder   String
  expiryMonth  Int
  expiryYear   Int
  cardType     String   // VISA, MASTERCARD, etc.
  isDefault    Boolean  @default(false)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  wallet       Wallet   @relation(fields: [walletId], references: [id], onDelete: Cascade)

  @@index([walletId])
}

// 优惠券系统模型
model Coupon {
  id           String       @id @default(cuid())
  code         String       @unique
  type         CouponType
  value        Float
  minPurchase  Float?
  maxDiscount  Float?
  currency     String       @default("USD")
  description  String
  validFrom    DateTime
  validUntil   DateTime
  usageLimit   Int?         // 使用次数限制
  usedCount    Int          @default(0)
  status       CouponStatus @default(ACTIVE)
  restrictions Json?        @default("{}")
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  userCoupons  UserCoupon[]

  @@index([code])
  @@index([status, validFrom, validUntil])
}

model UserCoupon {
  id       String   @id @default(cuid())
  userId   String
  couponId String
  usedAt   DateTime?
  orderId  String?
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  coupon   Coupon   @relation(fields: [couponId], references: [id], onDelete: Cascade)

  @@unique([userId, couponId])
  @@index([userId])
}

model PackageUsage {
  id            String   @id @default(cuid())
  userId        String
  orderId       String
  packageId     String
  packageName   String
  totalData     Float
  usedData      Float    @default(0)
  remainingData Float
  validUntil    DateTime
  status        String   @default("active")
  lastUsedAt    DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
}

model RechargeHistory {
  id        String   @id @default(cuid())
  userId    String
  amount    Float
  currency  String   @default("USD")
  method    String   // 充值方式
  status    String   @default("completed")
  reference String?  // 外部参考号
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
}

model SocialAccount {
  id         String   @id @default(cuid())
  userId     String
  provider   String   // google, facebook, etc.
  providerId String   // 第三方平台的用户ID
  email      String?
  name       String?
  avatar     String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerId])
  @@index([userId])
}

model ProductView {
  id        String   @id @default(cuid())
  userId    String
  productId String
  viewedAt  DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@index([userId])
  @@index([productId])
}

model Upload {
  id        String   @id @default(cuid())
  userId    String
  filename  String
  originalName String
  mimeType  String
  size      Int
  url       String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

// 地理信息模型
model Continent {
  id        String    @id @default(cuid())
  code      String    @unique // 'asia', 'europe', etc.
  nameEn    String
  nameZh    String
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  countries Country[]

  @@index([isActive])
}

model Country {
  id           String     @id @default(cuid())
  code         String     @unique // ISO country code
  nameEn       String
  nameZh       String
  continentId  String
  flagUrl      String?
  currency     String?
  timezone     String?
  isActive     Boolean    @default(true)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  continent    Continent  @relation(fields: [continentId], references: [id])
  operators    MobileOperator[]

  @@index([continentId])
  @@index([isActive])
}

model MobileOperator {
  id        String   @id @default(cuid())
  countryId String
  name      String
  code      String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  country   Country  @relation(fields: [countryId], references: [id])

  @@index([countryId])
  @@index([isActive])
}

// 页面和内容管理模型
model Page {
  id          String   @id @default(cuid())
  slug        String   @unique
  title       String
  content     String   @db.Text
  metaTitle   String?
  metaDesc    String?
  isPublished Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([slug])
  @@index([isPublished])
}

model Article {
  id          String   @id @default(cuid())
  title       String
  summary     String?
  content     String   @db.Text
  category    String
  imageUrl    String?
  author      String   @default("Yolloo Team")
  publishDate DateTime @default(now())
  readCount   Int      @default(0)
  likeCount   Int      @default(0)
  tags        String[]
  isPublished Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([category, isPublished])
  @@index([publishDate])
  @@index([tags])
}

model Banner {
  id          String   @id @default(cuid())
  title       String
  description String?
  imageUrl    String
  link        String?
  position    String   @default("home")
  isActive    Boolean  @default(true)
  priority    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([position, isActive])
  @@index([priority])
}

model HomeFeature {
  id          String   @id @default(cuid())
  title       String
  description String?
  icon        String?
  imageUrl    String?
  link        String?
  isActive    Boolean  @default(true)
  priority    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([isActive, priority])
}

model TravelTip {
  id          String   @id @default(cuid())
  title       String
  content     String?
  imageUrl    String
  link        String?
  category    String   @default("general")
  isActive    Boolean  @default(true)
  language    String   @default("en")
  priority    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([isActive, priority])
  @@index([language, category])
}

// 枚举类型定义
enum NotificationType {
  SYSTEM
  ORDER
  PROMOTION
  SECURITY
}

enum TransactionType {
  DEPOSIT
  PAYMENT
  REFUND
  WITHDRAWAL
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum CouponType {
  PERCENTAGE
  FIXED
}

enum CouponStatus {
  ACTIVE
  INACTIVE
  EXPIRED
}
